/**
 * AI Studio 2 API - DOM Controller
 *
 * 控制 AI Studio 页面的 DOM 元素操作
 */

import type { UIElements, ModelInfo, GenerationConfig } from '../types';
import { DOM_SELECTORS, FALLBACK_SELECTORS, TIMEOUTS } from '../config/constants';
import {
  logger,
  waitForElements,
  isElementVisible,
  isElementInteractable,
  simulateClick,
  delay
} from '../utils/helpers';
import { DOMError } from '../types';

export class DOMController {
  private elements: UIElements = {};
  private isInitialized = false;
  private modelList: ModelInfo[] = [];

  constructor() {
    this.bindMethods();
  }

  /**
   * 绑定方法到实例
   */
  private bindMethods(): void {
    this.findElement = this.findElement.bind(this);
    this.findElements = this.findElements.bind(this);
    this.waitForPageReady = this.waitForPageReady.bind(this);
  }

  /**
   * 初始化 DOM 控制器
   */
  public async initialize(): Promise<void> {
    try {
      logger.info('初始化 DOM 控制器...');

      await this.waitForPageReady();
      await this.findAllElements();

      this.isInitialized = true;
      logger.info('DOM 控制器初始化完成');
    } catch (error) {
      throw new DOMError(
        `DOM 控制器初始化失败: ${error instanceof Error ? error.message : String(error)}`,
        { originalError: error }
      );
    }
  }

  /**
   * 等待页面准备就绪
   */
  public async waitForPageReady(): Promise<void> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('页面加载超时'));
      }, TIMEOUTS.DOM_READY_TIMEOUT);

      const checkReady = () => {
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
          clearTimeout(timeout);
          resolve();
        } else {
          setTimeout(checkReady, 100);
        }
      };

      if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
          clearTimeout(timeout);
          resolve();
        }, { once: true });
      } else {
        checkReady();
      }
    });
  }

  /**
   * 查找单个元素
   */
  public async findElement(
    selector: string,
    fallbacks: string[] = [],
    timeout: number = TIMEOUTS.ELEMENT_WAIT
  ): Promise<Element> {
    const selectors = [selector, ...fallbacks];

    try {
      return await waitForElements(selectors, timeout);
    } catch (error) {
      throw new DOMError(
        `未找到元素: ${selector}`,
        { selector, fallbacks, timeout }
      );
    }
  }

  /**
   * 查找多个元素
   */
  public findElements(selector: string): NodeListOf<Element> {
    return document.querySelectorAll(selector);
  }

  /**
   * 查找所有必要的元素
   */
  private async findAllElements(): Promise<void> {
    try {
      // 查找输入框
      this.elements.promptInput = await this.findElement(
        DOM_SELECTORS.promptInput,
        [...FALLBACK_SELECTORS.promptInput]
      ) as HTMLTextAreaElement;

      // 查找提交按钮
      this.elements.submitButton = await this.findElement(
        DOM_SELECTORS.submitButton,
        [...FALLBACK_SELECTORS.submitButton]
      ) as HTMLButtonElement;

      // 查找响应容器（可选）
      try {
        this.elements.responseContainer = await this.findElement(
          DOM_SELECTORS.responseContainer,
          [],
          3000
        ) as HTMLElement;
      } catch {
        logger.warn('未找到响应容器，将在需要时重新查找');
      }

      // 查找模型选择器（可选）
      try {
        this.elements.modelSelector = await this.findElement(
          DOM_SELECTORS.modelSelector,
          [...FALLBACK_SELECTORS.modelSelector],
          3000
        ) as HTMLSelectElement;
      } catch {
        logger.warn('未找到模型选择器，模型切换功能可能不可用');
      }

      logger.info('成功找到主要 DOM 元素');
    } catch (error) {
      throw new DOMError(
        `查找 DOM 元素失败: ${error instanceof Error ? error.message : String(error)}`,
        { originalError: error }
      );
    }
  }

  /**
   * 发送消息到 AI Studio - 改进版本，参考Playwright实现
   */
  public async sendMessage(message: string): Promise<void> {
    if (!this.isInitialized) {
      throw new DOMError('DOM 控制器未初始化');
    }

    if (!this.elements.promptInput) {
      throw new DOMError('未找到输入框元素');
    }

    if (!isElementInteractable(this.elements.promptInput)) {
      throw new DOMError('输入框不可交互');
    }

    try {
      logger.info(`发送消息: ${message.substring(0, 50)}...`);

      // 使用JavaScript方式填充输入框，参考Playwright实现
      const textarea = this.elements.promptInput as HTMLTextAreaElement;

      // 清空并设置新值
      textarea.value = '';
      textarea.dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
      await delay(100);

      // 设置消息内容
      textarea.value = message;
      textarea.dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
      textarea.dispatchEvent(new Event('change', { bubbles: true, cancelable: true }));

      // 更新autosize wrapper的data-value属性
      const autosizeWrapper = document.querySelector('ms-prompt-input-wrapper ms-autosize-textarea') as HTMLElement;
      if (autosizeWrapper) {
        autosizeWrapper.setAttribute('data-value', message);
      }

      logger.info('输入框填充完成，等待提交按钮启用...');

      // 等待提交按钮启用
      await this.waitForSubmitButtonEnabled();

      // 使用键盘快捷键提交（更可靠）
      await this.submitViaKeyboard();

      logger.info('消息发送成功');
    } catch (error) {
      throw new DOMError(
        `发送消息失败: ${error instanceof Error ? error.message : String(error)}`,
        { message, originalError: error }
      );
    }
  }

  /**
   * 等待提交按钮启用
   */
  private async waitForSubmitButtonEnabled(): Promise<void> {
    const maxAttempts = 20;
    let attempts = 0;

    while (attempts < maxAttempts) {
      const submitButton = document.querySelector('button[aria-label="Run"].run-button') as HTMLButtonElement;

      if (submitButton && !submitButton.disabled && isElementInteractable(submitButton)) {
        logger.debug('提交按钮已启用');
        return;
      }

      await delay(250);
      attempts++;
    }

    throw new DOMError('等待提交按钮启用超时');
  }

  /**
   * 使用键盘快捷键提交消息
   */
  private async submitViaKeyboard(): Promise<void> {
    try {
      // 确保输入框有焦点
      if (this.elements.promptInput) {
        this.elements.promptInput.focus();
        await delay(100);
      }

      // 检测操作系统并使用相应的快捷键
      const isMac = navigator.platform.toLowerCase().includes('mac') ||
        navigator.userAgent.toLowerCase().includes('mac');

      const modifierKey = isMac ? 'Meta' : 'Control';

      logger.info(`使用快捷键提交: ${modifierKey}+Enter`);

      // 模拟按键事件
      const keydownEvent = new KeyboardEvent('keydown', {
        key: 'Enter',
        code: 'Enter',
        ctrlKey: !isMac,
        metaKey: isMac,
        bubbles: true,
        cancelable: true
      });

      const keyupEvent = new KeyboardEvent('keyup', {
        key: 'Enter',
        code: 'Enter',
        ctrlKey: !isMac,
        metaKey: isMac,
        bubbles: true,
        cancelable: true
      });

      // 发送事件到输入框
      if (this.elements.promptInput) {
        this.elements.promptInput.dispatchEvent(keydownEvent);
        await delay(50);
        this.elements.promptInput.dispatchEvent(keyupEvent);
      }

      // 验证提交是否成功（输入框应该被清空）
      await delay(750);

      let validationAttempts = 0;
      const maxValidationAttempts = 7;

      while (validationAttempts < maxValidationAttempts) {
        const currentValue = (this.elements.promptInput as HTMLTextAreaElement)?.value || '';
        if (currentValue === '') {
          logger.info('快捷键提交成功确认（输入框已清空）');
          return;
        }

        await delay(200);
        validationAttempts++;
      }

      logger.warn('快捷键提交后输入框未在预期时间内清空');
    } catch (error) {
      throw new DOMError(
        `键盘快捷键提交失败: ${error instanceof Error ? error.message : String(error)}`,
        { originalError: error }
      );
    }
  }

  /**
   * 提交消息
   */
  public async submitMessage(): Promise<void> {
    try {
      // 尝试重新查找提交按钮（可能页面状态已变化）
      let submitButton = this.elements.submitButton;

      if (!submitButton || !isElementInteractable(submitButton)) {
        logger.info('重新查找提交按钮...');

        // 尝试所有可能的提交按钮选择器
        const buttonSelectors = [
          'button[aria-label="Run"].run-button',
          'button[aria-label*="Send"]',
          'button[aria-label*="Submit"]',
          'button[data-testid*="send"]',
          'button.run-button',
          'button[type="submit"]',
          'button:has(svg):not([disabled])',
          'button:not([disabled]):not([aria-disabled="true"])'
        ];

        for (const selector of buttonSelectors) {
          const button = document.querySelector(selector) as HTMLButtonElement;
          if (button && isElementInteractable(button)) {
            submitButton = button;
            this.elements.submitButton = button;
            logger.info(`找到可用的提交按钮: ${selector}`);
            break;
          }
        }
      }

      if (!submitButton) {
        throw new DOMError('未找到提交按钮');
      }

      if (!isElementInteractable(submitButton)) {
        // 等待按钮变为可交互状态
        logger.info('等待提交按钮变为可交互状态...');
        let attempts = 0;
        const maxAttempts = 10;

        while (!isElementInteractable(submitButton) && attempts < maxAttempts) {
          await delay(500);
          attempts++;

          // 重新查找按钮
          const newButton = document.querySelector('button[aria-label="Run"].run-button') as HTMLButtonElement;
          if (newButton && isElementInteractable(newButton)) {
            submitButton = newButton;
            this.elements.submitButton = newButton;
            break;
          }
        }

        if (!isElementInteractable(submitButton)) {
          throw new DOMError('提交按钮在等待后仍不可交互');
        }
      }

      logger.info('点击提交按钮...');
      simulateClick(submitButton);
      await delay(200); // 等待提交处理

    } catch (error) {
      throw new DOMError(
        `提交消息失败: ${error instanceof Error ? error.message : String(error)}`,
        { originalError: error }
      );
    }
  }

  /**
   * 切换模型 - 按照原脚本的逻辑实现
   */
  public async switchModel(modelId: string): Promise<void> {
    if (!this.modelList.length) {
      throw new DOMError('模型列表未加载');
    }

    try {
      logger.info(`正在尝试将 AI Studio 模型切换为 ${modelId}...`);

      // 1. 找到 AI Studio 页面的模型选择器并点击以展开选项
      const studioModelSelector = document.querySelector('mat-select[data-test-ms-model-selector]');
      if (!studioModelSelector) {
        throw new DOMError('未找到 AI Studio 页面的模型选择器');
      }

      simulateClick(studioModelSelector);
      await delay(500); // 等待下拉菜单动画

      // 2. 从模型列表中找到 modelId 对应的 displayName
      let modelDisplayName = '';
      const modelEntry = this.modelList.find(m =>
        m.id === modelId || m.id.endsWith(modelId) || m.name === modelId
      );

      if (modelEntry) {
        modelDisplayName = modelEntry.displayName;
      } else {
        // 如果在模型列表中找不到，尝试直接使用 modelId
        modelDisplayName = modelId.includes('/') ? modelId.split('/')[1] : modelId;
        modelDisplayName = modelDisplayName
          .replace(/-/g, ' ')
          .replace(/\b\w/g, (l) => l.toUpperCase());
        logger.warn(`未在模型列表中找到 ${modelId} 的确切显示名称，将尝试使用 '${modelDisplayName}'`);
      }

      // 3. 在展开的选项中找到包含目标模型显示名称的 mat-option
      const optionsPane = document.querySelector('div.cdk-overlay-pane');
      if (!optionsPane) {
        await delay(1000); // 额外等待
        const optionsPaneRetry = document.querySelector('div.cdk-overlay-pane');
        if (!optionsPaneRetry) {
          throw new DOMError('未找到模型选项面板 (cdk-overlay-pane)');
        }
      }

      // 查找所有选项
      const allOptions = document.querySelectorAll(
        'div.cdk-overlay-pane mat-option, div.cdk-overlay-pane div[role="option"]'
      );

      logger.info(`尝试匹配显示名称: "${modelDisplayName}"`);

      let targetOption: Element | null = null;
      for (const option of allOptions) {
        const optionTextElement = option.querySelector(
          '.model-option-content span.gmat-body-medium, .mdc-list-item__primary-text'
        );
        if (optionTextElement) {
          const text = optionTextElement.textContent?.trim() || '';
          if (text.includes(modelDisplayName) || modelDisplayName.includes(text)) {
            targetOption = option;
            logger.info(`找到匹配选项: "${text}" for "${modelDisplayName}"`);
            break;
          }
        }
      }

      // 如果精确匹配失败，尝试模糊匹配
      if (!targetOption) {
        const simplifiedModelDisplayName = modelDisplayName.replace(/[\d.-]+$/, '').trim();
        for (const option of allOptions) {
          const optionTextElement = option.querySelector(
            '.model-option-content span.gmat-body-medium, .mdc-list-item__primary-text'
          );
          if (optionTextElement) {
            const text = optionTextElement.textContent?.trim() || '';
            if (text.toLowerCase().includes(simplifiedModelDisplayName.toLowerCase())) {
              targetOption = option;
              logger.info(`通过模糊匹配找到选项: "${text}" for simplified "${simplifiedModelDisplayName}"`);
              break;
            }
          }
        }
      }

      if (!targetOption) {
        logger.error(`在选项中未找到模型 "${modelDisplayName}". 可用选项:`);
        allOptions.forEach((opt) => {
          const optTextEl = opt.querySelector(
            '.model-option-content span.gmat-body-medium, .mdc-list-item__primary-text'
          );
          if (optTextEl) {
            logger.info(` - ${optTextEl.textContent?.trim()}`);
          }
        });
        // 尝试关闭下拉框
        simulateClick(studioModelSelector);
        throw new DOMError(`在选项中未找到模型 "${modelDisplayName}"`);
      }

      // 4. 点击找到的选项
      simulateClick(targetOption);
      await delay(300); // 等待选择生效

      logger.info(`AI Studio 模型已成功切换为 ${modelDisplayName} (ID: ${modelId})`);
    } catch (error) {
      throw new DOMError(
        `模型切换失败: ${error instanceof Error ? error.message : String(error)}`,
        { modelId, originalError: error }
      );
    }
  }

  /**
   * 清空聊天记录 - 按照原脚本的逻辑实现
   */
  public async clearChat(): Promise<void> {
    try {
      logger.info('正在清空聊天记录...');

      // 查找清空聊天按钮
      const clearButton = document.querySelector(
        'button[data-test-clear="outside"][aria-label="Clear chat"]'
      ) as HTMLButtonElement;

      if (!clearButton) {
        logger.warn('未找到清空聊天按钮，跳过清空操作');
        return;
      }

      if (!isElementInteractable(clearButton)) {
        logger.warn('清空聊天按钮不可交互，跳过清空操作');
        return;
      }

      // 点击清空按钮
      simulateClick(clearButton);
      await delay(500); // 等待确认对话框出现

      // 查找确认按钮
      const confirmButton = document.querySelector(
        'button.mdc-button:has-text("Continue"), button[data-test="confirm-clear"]'
      ) as HTMLButtonElement;

      if (confirmButton && isElementInteractable(confirmButton)) {
        simulateClick(confirmButton);
        await delay(1000); // 等待清空完成
        logger.info('聊天记录已清空');
      } else {
        logger.warn('未找到确认清空按钮');
      }
    } catch (error) {
      logger.error(`清空聊天失败: ${error instanceof Error ? error.message : String(error)}`);
      // 不抛出错误，因为清空失败不应该阻止后续操作
    }
  }

  /**
   * 执行完整的聊天请求
   */
  public async executeChatRequest(data: {
    messages: any[];
    model?: string;
    parameters?: any;
    stream?: boolean;
    actions?: any[];
  }): Promise<void> {
    try {
      logger.info('开始执行聊天请求:', data);

      // 执行操作序列
      if (data.actions) {
        for (const action of data.actions) {
          await this.executeAction(action);
        }
      } else {
        // 默认操作序列
        await this.clearChat();

        if (data.model) {
          await this.switchModel(data.model);
        }

        if (data.parameters) {
          await this.setGenerationConfig(data.parameters);
        }

        // 准备并发送消息
        const prompt = this.preparePromptFromMessages(data.messages);
        await this.sendMessage(prompt);
      }

      logger.info('聊天请求执行完成');
    } catch (error) {
      throw new DOMError(
        `执行聊天请求失败: ${error instanceof Error ? error.message : String(error)}`,
        { data, originalError: error }
      );
    }
  }

  /**
   * 执行单个操作
   */
  private async executeAction(action: any): Promise<void> {
    try {
      switch (action.type) {
        case 'clear_chat':
          if (action.enabled !== false) {
            await this.clearChat();
          }
          break;

        case 'set_model':
          if (action.model) {
            await this.switchModel(action.model);
          }
          break;

        case 'set_parameters':
          if (action.parameters) {
            await this.setGenerationConfig(action.parameters);
          }
          break;

        case 'send_message':
          if (action.content) {
            await this.sendMessage(action.content);
          }
          break;

        case 'wait_for_response':
          // 等待响应的逻辑由拦截器处理
          logger.info('等待响应...');
          break;

        default:
          logger.warn(`未知操作类型: ${action.type}`);
      }
    } catch (error) {
      logger.error(`执行操作失败: ${action.type}`, error);
      throw error;
    }
  }

  /**
   * 从消息列表准备提示文本
   */
  private preparePromptFromMessages(messages: any[]): string {
    const promptParts: string[] = [];

    for (const message of messages) {
      const role = message.role || '';
      const content = message.content || '';

      if (role === 'system') {
        promptParts.push(`系统指令:\n${content}`);
      } else if (role === 'user') {
        promptParts.push(`用户:\n${content}`);
      } else if (role === 'assistant') {
        promptParts.push(`助手:\n${content}`);
      }
    }

    return promptParts.join('\n\n---\n\n');
  }

  /**
   * 设置生成参数
   */
  public async setGenerationConfig(config: GenerationConfig): Promise<void> {
    try {
      logger.info('设置生成参数:', config);

      if (config.temperature !== undefined) {
        await this.setTemperature(config.temperature);
      }

      if (config.topP !== undefined) {
        await this.setTopP(config.topP);
      }

      if (config.maxOutputTokens !== undefined) {
        await this.setMaxTokens(config.maxOutputTokens);
      }

      if (config.stopSequences !== undefined) {
        await this.setStopSequences(config.stopSequences);
      }

      logger.info('生成参数设置完成');
    } catch (error) {
      throw new DOMError(
        `设置生成参数失败: ${error instanceof Error ? error.message : String(error)}`,
        { config, originalError: error }
      );
    }
  }

  /**
   * 设置温度参数 - 按照原脚本的逻辑实现
   */
  private async setTemperature(temperature: number): Promise<void> {
    try {
      logger.info(`正在设置 temperature 为 ${temperature}...`);

      let targetElement = document.querySelector(
        'div[data-test-id="temperatureSliderContainer"] input[type="number"].slider-input'
      ) as HTMLInputElement;

      if (!targetElement) {
        // Fallback if specific selector fails
        const labels = Array.from(document.querySelectorAll('h3.gmat-body-medium'));
        for (const label of labels) {
          if (label.textContent?.trim().toLowerCase() === 'temperature') {
            targetElement = label
              .closest('.settings-item-column')
              ?.querySelector('input[type="number"].slider-input') as HTMLInputElement;
            if (targetElement) break;
          }
        }
      }

      if (targetElement) {
        this.fillInputField(targetElement, temperature.toString());
        logger.info('temperature 设置成功！');
      } else {
        logger.error('未找到 AI Studio 页面上 temperature 对应的DOM元素');
      }

      await delay(TIMEOUTS.PARAMETER_SET_DELAY);
    } catch (error) {
      logger.error(`设置 temperature 失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 设置 Top P 参数 - 按照原脚本的逻辑实现
   */
  private async setTopP(topP: number): Promise<void> {
    try {
      logger.info(`正在设置 topP 为 ${topP}...`);

      // 查找所有 h3.gmat-body-medium 元素
      const allH3s = document.querySelectorAll('h3.gmat-body-medium');
      let topPLabel: Element | null = null;
      for (const h3 of allH3s) {
        if (h3.textContent?.trim().toLowerCase() === 'top p') {
          topPLabel = h3;
          break;
        }
      }

      let targetElement: HTMLInputElement | null = null;
      if (topPLabel) {
        const parentColumn = topPLabel.closest('.settings-item-column');
        if (parentColumn) {
          targetElement = parentColumn.querySelector(
            'input[type="number"].slider-input'
          ) as HTMLInputElement;
        }
      }

      if (!targetElement) {
        // 保留原始的 fallback 逻辑
        const labels = Array.from(document.querySelectorAll('h3.gmat-body-medium'));
        for (const label of labels) {
          if (label.textContent?.trim().toLowerCase() === 'top p') {
            targetElement = label
              .closest('.settings-item-column')
              ?.querySelector('input[type="number"].slider-input') as HTMLInputElement;
            if (targetElement) break;
          }
        }
      }

      if (targetElement) {
        this.fillInputField(targetElement, topP.toString());
        logger.info('topP 设置成功！');
      } else {
        logger.error('未找到 AI Studio 页面上 topP 对应的DOM元素');
      }

      await delay(TIMEOUTS.PARAMETER_SET_DELAY);
    } catch (error) {
      logger.error(`设置 topP 失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 设置最大输出令牌数 - 按照原脚本的逻辑实现
   */
  private async setMaxTokens(maxTokens: number): Promise<void> {
    try {
      logger.info(`正在设置 maxOutputTokens 为 ${maxTokens}...`);

      let targetElement = document.querySelector(
        'input[aria-label="Maximum output tokens"]'
      ) as HTMLInputElement;

      if (!targetElement) {
        // Fallback
        const labels = Array.from(document.querySelectorAll('h3.gmat-body-medium'));
        for (const label of labels) {
          if (label.textContent?.trim().toLowerCase() === 'output length') {
            // "Output length" seems to be the label for max tokens
            targetElement = label
              .closest('.settings-item-column, .settings-item')
              ?.querySelector('input[type="number"]') as HTMLInputElement;
            if (targetElement) break;
          }
        }
      }

      if (targetElement) {
        this.fillInputField(targetElement, maxTokens.toString());
        logger.info('maxOutputTokens 设置成功！');
      } else {
        logger.error('未找到 AI Studio 页面上 maxOutputTokens 对应的DOM元素');
      }

      await delay(TIMEOUTS.PARAMETER_SET_DELAY);
    } catch (error) {
      logger.error(`设置 maxOutputTokens 失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 设置停止序列 - 按照原脚本的逻辑实现
   */
  private async setStopSequences(stopSequences: string[]): Promise<void> {
    try {
      logger.info(`正在设置 stopSequences 为 ${JSON.stringify(stopSequences)}...`);

      // 停止序列比较特殊，它是一个chips input
      const stopInput = document.querySelector(
        'input[aria-label="Add stop token"]'
      ) as HTMLInputElement;

      if (!stopInput) {
        logger.error('未找到 AI Studio 页面上 stopSequences 对应的DOM元素');
        return;
      }

      const existingChips = document.querySelectorAll(
        'mat-chip-grid mat-chip-row button[aria-label*="Remove"]'
      );

      // 1. 清除现有停止序列
      for (const chipRemoveButton of existingChips) {
        simulateClick(chipRemoveButton);
        await delay(50); // 短暂等待移除生效
      }

      // 2. 添加新的停止序列
      if (stopSequences && Array.isArray(stopSequences)) {
        for (const seq of stopSequences) {
          if (seq.trim()) {
            stopInput.value = seq.trim();
            stopInput.dispatchEvent(new Event('input', { bubbles: true }));
            // 模拟回车添加
            const enterEvent = new KeyboardEvent('keydown', {
              key: 'Enter',
              code: 'Enter',
              bubbles: true,
            });
            stopInput.dispatchEvent(enterEvent);
            await delay(100); // 等待chip添加
          }
        }
      }

      logger.info('stopSequences 设置成功！');
    } catch (error) {
      logger.error(`设置 stopSequences 失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 获取响应文本 - 改进版本，支持多种选择器
   */
  public async getResponseText(): Promise<string> {
    try {
      logger.debug('开始获取响应文本...');

      // 尝试多种响应容器选择器
      const responseSelectors = [
        'ms-chat-turn .chat-turn-container.model',
        'ms-chat-turn.model',
        '.chat-turn-container.model',
        '.model-response',
        '[data-test-id*="response"]',
        '.response-container',
        '.chat-response',
        'ms-chat-turn:last-child',
        '.chat-turn:last-child'
      ];

      let responseContainer: HTMLElement | null = null;

      // 尝试找到响应容器
      for (const selector of responseSelectors) {
        const container = document.querySelector(selector) as HTMLElement;
        if (container) {
          responseContainer = container;
          logger.debug(`找到响应容器: ${selector}`);
          break;
        }
      }

      if (!responseContainer) {
        logger.warn('未找到响应容器，尝试查找最后一个聊天消息');
        // 作为备选，查找最后一个聊天消息
        const allChatTurns = document.querySelectorAll('ms-chat-turn, .chat-turn, .message');
        if (allChatTurns.length > 0) {
          responseContainer = allChatTurns[allChatTurns.length - 1] as HTMLElement;
          logger.debug('使用最后一个聊天消息作为响应容器');
        }
      }

      if (!responseContainer) {
        logger.warn('完全未找到响应容器');
        return '';
      }

      // 尝试多种文本选择器
      const textSelectors = [
        'ms-cmark-node.cmark-node',
        'ms-cmark-node',
        '.cmark-node',
        '.message-content',
        '.response-text',
        '.chat-content',
        'p',
        'div[data-test-id*="content"]',
        '.markdown-content'
      ];

      let responseText = '';

      for (const selector of textSelectors) {
        const textElements = responseContainer.querySelectorAll(selector);
        if (textElements.length > 0) {
          responseText = Array.from(textElements)
            .map(el => el.textContent?.trim() || '')
            .filter(text => text.length > 0)
            .join('\n');

          if (responseText) {
            logger.debug(`找到响应文本，使用选择器: ${selector}, 长度: ${responseText.length}`);
            break;
          }
        }
      }

      // 如果还是没找到，直接使用容器的文本内容
      if (!responseText) {
        responseText = responseContainer.textContent?.trim() || '';
        logger.debug(`使用容器直接文本，长度: ${responseText.length}`);
      }

      logger.debug(`最终响应文本: ${responseText.substring(0, 100)}...`);
      return responseText;

    } catch (error) {
      logger.error('获取响应文本失败:', error);
      return '';
    }
  }

  /**
   * 检查是否正在生成 - 改进版本，支持多种检测方式
   */
  public isGenerating(): boolean {
    try {
      // 尝试多种加载指示器选择器
      const loadingSelectors = [
        'button[aria-label="Run"].run-button svg .stoppable-spinner',
        '.stoppable-spinner',
        '.loading-spinner',
        '.spinner',
        '[data-test-id*="loading"]',
        '[data-test-id*="generating"]',
        'button[aria-label="Stop"]',
        'button[aria-label*="Stop"]',
        '.generating-indicator'
      ];

      // 检查是否有加载指示器
      for (const selector of loadingSelectors) {
        const element = document.querySelector(selector);
        if (element && isElementVisible(element)) {
          logger.debug(`检测到生成中，使用选择器: ${selector}`);
          return true;
        }
      }

      // 检查提交按钮状态
      const submitButton = document.querySelector('button[aria-label="Run"].run-button') as HTMLButtonElement;
      if (submitButton) {
        // 如果按钮被禁用或者有特定的类名，可能表示正在生成
        if (submitButton.disabled || submitButton.classList.contains('generating')) {
          logger.debug('检测到生成中，基于提交按钮状态');
          return true;
        }

        // 检查按钮文本是否变为"Stop"
        const buttonText = submitButton.textContent?.trim().toLowerCase();
        if (buttonText && (buttonText.includes('stop') || buttonText.includes('停止'))) {
          logger.debug('检测到生成中，基于按钮文本');
          return true;
        }
      }

      // 检查是否有"正在思考"或类似的文本
      const thinkingTexts = ['thinking', '思考中', 'generating', '生成中', 'processing', '处理中'];
      for (const text of thinkingTexts) {
        const element = document.querySelector(`*:contains("${text}")`);
        if (element && isElementVisible(element)) {
          logger.debug(`检测到生成中，基于文本: ${text}`);
          return true;
        }
      }

      return false;
    } catch (error) {
      logger.debug('检查生成状态失败:', error);
      return false;
    }
  }

  /**
   * 等待生成完成 - 改进版本，添加更多调试信息
   */
  public async waitForGenerationComplete(): Promise<void> {
    const startTime = Date.now();
    let lastLogTime = startTime;
    let isFirstCheck = true;

    logger.info('开始等待生成完成...');

    while (this.isGenerating()) {
      const currentTime = Date.now();
      const elapsed = currentTime - startTime;

      // 每5秒记录一次状态
      if (currentTime - lastLogTime > 5000 || isFirstCheck) {
        logger.info(`等待生成完成中... 已等待 ${Math.round(elapsed / 1000)}s`);
        lastLogTime = currentTime;
        isFirstCheck = false;
      }

      if (elapsed > TIMEOUTS.RESPONSE_TIMEOUT) {
        logger.error(`等待生成完成超时，已等待 ${Math.round(elapsed / 1000)}s`);
        throw new DOMError('等待生成完成超时');
      }

      await delay(TIMEOUTS.RESPONSE_POLL_INTERVAL);
    }

    const totalTime = Date.now() - startTime;
    logger.info(`生成完成，总耗时: ${Math.round(totalTime / 1000)}s`);
  }

  /**
   * 设置模型列表
   */
  public setModelList(models: ModelInfo[]): void {
    this.modelList = models;
    logger.debug(`设置模型列表: ${models.length} 个模型`);
  }

  /**
   * 获取当前页面状态
   */
  public getPageState(): { isReady: boolean; hasInput: boolean; isGenerating: boolean } {
    return {
      isReady: this.isInitialized,
      hasInput: !!this.elements.promptInput && isElementInteractable(this.elements.promptInput),
      isGenerating: this.isGenerating(),
    };
  }

  /**
   * 刷新元素引用
   */
  public async refreshElements(): Promise<void> {
    this.elements = {};
    await this.findAllElements();
  }

  /**
   * 获取当前页面的参数值
   */
  public getCurrentParameters(): GenerationConfig {
    const config: GenerationConfig = {};

    try {
      // 获取温度值
      const tempInput = document.querySelector(
        'div[data-test-id="temperatureSliderContainer"] input[type="number"].slider-input'
      ) as HTMLInputElement;
      if (tempInput && tempInput.value) {
        config.temperature = parseFloat(tempInput.value);
      }

      // 获取 Top P 值
      const allH3s = document.querySelectorAll('h3.gmat-body-medium');
      for (const h3 of allH3s) {
        if (h3.textContent?.trim().toLowerCase() === 'top p') {
          const parentColumn = h3.closest('.settings-item-column');
          if (parentColumn) {
            const topPInput = parentColumn.querySelector(
              'input[type="number"].slider-input'
            ) as HTMLInputElement;
            if (topPInput && topPInput.value) {
              config.topP = parseFloat(topPInput.value);
            }
          }
          break;
        }
      }

      // 获取最大令牌数
      const maxTokensInput = document.querySelector(
        'input[aria-label="Maximum output tokens"]'
      ) as HTMLInputElement;
      if (maxTokensInput && maxTokensInput.value) {
        config.maxOutputTokens = parseInt(maxTokensInput.value, 10);
      }

      // 获取停止序列
      const stopChips = document.querySelectorAll('mat-chip-grid mat-chip-row');
      const stopSequences: string[] = [];
      for (const chip of stopChips) {
        const text = chip.textContent?.trim();
        if (text && !text.includes('Remove')) {
          stopSequences.push(text.replace(/\s*Remove\s*$/, '').trim());
        }
      }
      if (stopSequences.length > 0) {
        config.stopSequences = stopSequences;
      }
    } catch (error) {
      logger.warn('获取当前参数失败:', error);
    }

    return config;
  }

  /**
   * 获取当前选中的模型
   */
  public getCurrentModel(): string | null {
    try {
      const modelSelector = document.querySelector('mat-select[data-test-ms-model-selector]');
      if (modelSelector) {
        const selectedText = modelSelector.textContent?.trim();
        if (selectedText) {
          // 尝试从模型列表中找到匹配的模型ID
          const matchedModel = this.modelList.find(m =>
            m.displayName === selectedText || m.name === selectedText
          );
          return matchedModel ? matchedModel.id : null;
        }
      }
    } catch (error) {
      logger.warn('获取当前模型失败:', error);
    }
    return null;
  }

  /**
   * 填充输入字段 - 模拟 Playwright 的 fill 方法
   */
  private fillInputField(element: HTMLInputElement, value: string): void {
    // 清空现有值
    element.value = '';
    element.dispatchEvent(new Event('input', { bubbles: true }));

    // 设置新值
    element.value = value;

    // 触发事件序列，确保页面响应
    element.dispatchEvent(new Event('input', { bubbles: true, cancelable: true }));
    element.dispatchEvent(new Event('change', { bubbles: true, cancelable: true }));

    // 触发焦点事件
    element.dispatchEvent(new Event('blur', { bubbles: true }));

    logger.debug(`填充输入字段: ${value}`);
  }
}
