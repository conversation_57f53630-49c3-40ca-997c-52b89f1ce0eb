/**
 * AI Studio 2 API - Configuration Constants
 * 
 * 项目中使用的所有常量配置
 */

import type { DOMSelectors, UserscriptConfig } from '../types';

// --- 服务器配置 ---
export const SERVER_CONFIG = {
  DEFAULT_HOST: 'localhost',
  DEFAULT_PORT: 8000,
  WS_ENDPOINT: '/ws/aistudio',  // 使用 AI Studio WebSocket 端点
  API_ENDPOINT: '/v1/chat/completions',
  MODELS_ENDPOINT: '/v1/models',
} as const;

// --- AI Studio URLs ---
export const AI_STUDIO_URLS = {
  BASE: 'https://aistudio.google.com',
  LIST_MODELS: 'https://alkalimakersuite-pa.clients6.google.com/$rpc/google.internal.alkali.applications.makersuite.v1.MakerSuiteService/ListModels',
  GENERATE_CONTENT: 'https://alkalimakersuite-pa.clients6.google.com/$rpc/google.internal.alkali.applications.makersuite.v1.MakerSuiteService/GenerateContent',
} as const;

// --- DOM 选择器 ---
export const DOM_SELECTORS: DOMSelectors = {
  // 输入相关
  promptInput: 'ms-prompt-input-wrapper ms-autosize-textarea textarea',
  submitButton: 'button[aria-label="Run"].run-button',

  // 响应相关
  responseContainer: 'ms-chat-turn .chat-turn-container.model',
  responseText: 'ms-cmark-node.cmark-node',
  loadingSpinner: 'button[aria-label="Run"].run-button svg .stoppable-spinner',

  // 模型和参数设置
  modelSelector: 'mat-select[data-test-ms-model-selector]',
  temperatureInput: 'div[data-test-id="temperatureSliderContainer"] input[type="number"].slider-input',
  topPInput: 'div.settings-item-column:has(h3:text-is("Top P")) input[type="number"].slider-input',
  maxTokensInput: 'input[aria-label="Maximum output tokens"]',
  stopSequenceInput: 'input[aria-label="Add stop token"]',
};

// --- 备用选择器 ---
export const FALLBACK_SELECTORS = {
  promptInput: [
    'textarea[aria-label="输入消息"]',
    'textarea[aria-label="Message input"]',
    'textarea[placeholder*="Enter a prompt here"]',
    'textarea[placeholder*="Send a message"]',
    'div[contenteditable="true"][role="textbox"]',
    'textarea:not([readonly]):not([disabled])',
    'div[contenteditable="true"]:not([aria-disabled="true"])',
  ],
  submitButton: [
    'button[aria-label*="Send"]',
    'button[aria-label*="Submit"]',
    'button[data-testid*="send"]',
    'button > span[class*="send"]',
    'button[aria-label="Run"]',
    'button.run-button',
    'button[type="submit"]',
    'button:has(svg)',
    'button:not([disabled]):not([aria-disabled="true"])',
  ],
  modelSelector: [
    'mat-select[data-test-ms-model-selector]',
    'select[aria-label*="model"]',
    'div[role="combobox"][aria-label*="model"]',
  ],
} as const;

// --- 默认配置 ---
export const DEFAULT_CONFIG: UserscriptConfig = {
  serverUrl: `http://${SERVER_CONFIG.DEFAULT_HOST}:${SERVER_CONFIG.DEFAULT_PORT}`,
  wsUrl: `ws://${SERVER_CONFIG.DEFAULT_HOST}:${SERVER_CONFIG.DEFAULT_PORT}${SERVER_CONFIG.WS_ENDPOINT}`,
  debug: false,
  autoConnect: true,
  retryAttempts: 3,
  retryDelay: 1000,
};

// --- 超时配置 ---
export const TIMEOUTS = {
  ELEMENT_WAIT: 10000,
  REQUEST_TIMEOUT: 30000,
  WS_CONNECT_TIMEOUT: 5000,
  WS_RECONNECT_DELAY: 3000,
  DOM_READY_TIMEOUT: 15000,
  MODEL_SWITCH_DELAY: 1000,
  PARAMETER_SET_DELAY: 300,
  RESPONSE_POLL_INTERVAL: 200,
  RESPONSE_TIMEOUT: 300000, // 5 minutes
} as const;

// --- 重试配置 ---
export const RETRY_CONFIG = {
  MAX_ATTEMPTS: 3,
  INITIAL_DELAY: 1000,
  BACKOFF_MULTIPLIER: 2,
  MAX_DELAY: 10000,
} as const;

// --- 消息类型 ---
export const MESSAGE_TYPES = {
  // 发送到服务器
  USERSCRIPT_READY: 'userscript_ready',
  API_RESPONSE: 'api_response',
  API_STREAM_CHUNK: 'api_stream_chunk',
  API_STREAM_END: 'api_stream_end',
  API_ERROR: 'api_error',

  // 从服务器接收
  API_REQUEST: 'api_request',
  CONNECTION_STATUS: 'connection_status',
  SERVER_ERROR: 'server_error',
} as const;

// --- 模型配置 ---
export const MODEL_CONFIG = {
  DEFAULT_TEMPERATURE: 0.15,
  DEFAULT_TOP_P: 0.95,
  DEFAULT_MAX_TOKENS: 65536,
  DEFAULT_TOP_K: 40,

  // 温度范围
  TEMPERATURE_MIN: 0,
  TEMPERATURE_MAX: 2,
  TEMPERATURE_STEP: 0.05,

  // Top P 范围
  TOP_P_MIN: 0,
  TOP_P_MAX: 1,
  TOP_P_STEP: 0.05,

  // Token 范围
  MAX_TOKENS_MIN: 1,
  MAX_TOKENS_MAX: 1000000,
  MAX_TOKENS_STEP: 1,
} as const;

// --- 错误代码 ---
export const ERROR_CODES = {
  // 认证错误
  AUTH_FAILED: 'AUTH_FAILED',
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  SESSION_EXPIRED: 'SESSION_EXPIRED',

  // 网络错误
  NETWORK_ERROR: 'NETWORK_ERROR',
  CONNECTION_FAILED: 'CONNECTION_FAILED',
  REQUEST_TIMEOUT: 'REQUEST_TIMEOUT',

  // DOM 错误
  ELEMENT_NOT_FOUND: 'ELEMENT_NOT_FOUND',
  ELEMENT_NOT_INTERACTIVE: 'ELEMENT_NOT_INTERACTIVE',
  DOM_MANIPULATION_FAILED: 'DOM_MANIPULATION_FAILED',

  // API 错误
  INVALID_REQUEST: 'INVALID_REQUEST',
  MODEL_NOT_AVAILABLE: 'MODEL_NOT_AVAILABLE',
  GENERATION_FAILED: 'GENERATION_FAILED',
  RATE_LIMITED: 'RATE_LIMITED',

  // 系统错误
  INITIALIZATION_FAILED: 'INITIALIZATION_FAILED',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
} as const;

// --- 日志配置 ---
export const LOG_CONFIG = {
  MAX_LOG_ENTRIES: 1000,
  LOG_RETENTION_DAYS: 7,
  DEFAULT_LEVEL: 'info' as const,
  CONSOLE_PREFIX: '[AI Studio 2 API]',
} as const;

// --- 存储键名 ---
export const STORAGE_KEYS = {
  API_KEY: 'aistudio_api_key',
  CONFIG: 'aistudio_config',
  AUTH_CREDENTIALS: 'aistudio_auth',
  MODEL_CACHE: 'aistudio_models',
  DEBUG_LOGS: 'aistudio_debug_logs',
  USER_PREFERENCES: 'aistudio_preferences',
} as const;

// --- UI 配置 ---
export const UI_CONFIG = {
  DEBUG_PANEL: {
    WIDTH: 400,
    HEIGHT: 600,
    MIN_WIDTH: 300,
    MIN_HEIGHT: 400,
    Z_INDEX: 99999,
    POSITION: {
      TOP: 20,
      RIGHT: 20,
    },
  },
  NOTIFICATION: {
    DURATION: 3000,
    FADE_DURATION: 300,
  },
  ANIMATION: {
    DURATION: 200,
    EASING: 'ease-in-out',
  },
} as const;

// --- 版本信息 ---
export const VERSION_INFO = {
  VERSION: '0.0.1',
  BUILD_DATE: new Date().toISOString(),
  AUTHOR: 'lll9p',
  REPOSITORY: 'https://github.com/lll9p/aistudio2api',
} as const;

// --- 功能标志 ---
export const FEATURE_FLAGS = {
  ENABLE_DEBUG_PANEL: true,
  ENABLE_AUTO_RETRY: true,
  ENABLE_STREAM_RESPONSE: true,
  ENABLE_MODEL_SWITCHING: true,
  ENABLE_PARAMETER_SYNC: true,
  ENABLE_ERROR_REPORTING: true,
} as const;

// --- 正则表达式 ---
export const REGEX_PATTERNS = {
  MODEL_ID: /^models\/[a-zA-Z0-9-_.]+$/,
  API_KEY: /^[a-zA-Z0-9_-]{20,}$/,
  URL: /^https?:\/\/.+/,
  JSON_RESPONSE: /^\s*[\[{]/,
} as const;

// --- MIME 类型 ---
export const MIME_TYPES = {
  JSON: 'application/json',
  TEXT: 'text/plain',
  HTML: 'text/html',
  FORM_DATA: 'application/x-www-form-urlencoded',
  MULTIPART: 'multipart/form-data',
} as const;

// --- HTTP 状态码 ---
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
} as const;
