#!/usr/bin/env python3
"""
测试userscript消息发送功能
模拟userscript连接并发送消息到server
"""

import asyncio
import json
import logging

import websockets

# 配置日志
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

SERVER_WS_URL = "ws://localhost:8000/ws/aistudio"


async def test_userscript_connection():
    """测试userscript连接和消息发送"""

    try:
        logger.info("🔌 连接到 AI Studio WebSocket...")
        async with websockets.connect(SERVER_WS_URL) as websocket:
            logger.info("✅ WebSocket 连接成功")

            # 发送userscript就绪消息
            ready_message = {
                "type": "userscript_ready",
                "data": {
                    "models": [
                        {"id": "gemini-1.5-pro", "name": "Gemini 1.5 Pro"},
                        {"id": "gemini-1.5-flash", "name": "Gemini 1.5 Flash"},
                        {"id": "gemini-1.0-pro", "name": "Gemini 1.0 Pro"},
                    ],
                    "capabilities": ["text_generation", "model_switching"],
                    "version": "1.0.0",
                },
            }

            await websocket.send(json.dumps(ready_message))
            logger.info("📤 已发送userscript就绪消息")

            # 等待服务器响应
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                response_data = json.loads(response)
                logger.info(f"📥 收到服务器响应: {response_data.get('type', 'unknown')}")
                logger.info(f"   响应内容: {response_data}")
            except asyncio.TimeoutError:
                logger.warning("⏰ 等待服务器响应超时")

            # 保持连接一段时间，监听可能的聊天请求
            logger.info("👂 监听聊天请求...")
            try:
                while True:
                    message = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                    data = json.loads(message)
                    message_type = data.get("type")

                    logger.info(f"📨 收到消息: {message_type}")

                    if message_type in ["chat_request", "execute_chat_request"]:
                        # 模拟处理聊天请求
                        request_id = data.get("requestId")
                        logger.info(f"💬 处理聊天请求: {request_id}")
                        logger.info(f"   请求数据: {data}")

                        # 发送模拟响应
                        response_message = {
                            "type": "api_response",
                            "requestId": request_id,
                            "data": {"content": "你好！我是AI助手，很高兴为您服务！", "finishReason": "stop"},
                        }

                        await websocket.send(json.dumps(response_message))
                        logger.info("📤 已发送聊天响应")

            except asyncio.TimeoutError:
                logger.info("⏰ 监听超时，连接保持中...")

    except Exception as e:
        logger.error(f"❌ 连接失败: {e}")


async def test_chat_request():
    """测试发送聊天请求"""
    import aiohttp

    logger.info("💬 测试聊天请求...")

    chat_data = {"model": "gemini-2.0-flash", "messages": [{"role": "user", "content": "你好"}], "stream": False}

    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                "http://localhost:8000/v1/aistudio/chat/completions",
                json=chat_data,
                headers={"Content-Type": "application/json"},
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    logger.info(f"✅ 聊天响应: {result}")
                else:
                    error_text = await response.text()
                    logger.error(f"❌ 聊天请求失败: {response.status} - {error_text}")

    except Exception as e:
        logger.error(f"❌ 聊天请求异常: {e}")


async def main():
    """主函数"""
    logger.info("🚀 开始测试userscript消息功能")
    logger.info("=" * 50)

    # 创建两个任务：一个模拟userscript连接，一个发送聊天请求
    userscript_task = asyncio.create_task(test_userscript_connection())

    # 等待userscript连接建立
    await asyncio.sleep(2)

    # 发送聊天请求
    await test_chat_request()

    # 等待userscript任务完成或超时
    try:
        await asyncio.wait_for(userscript_task, timeout=10)
    except asyncio.TimeoutError:
        logger.info("⏰ userscript连接测试超时")
        userscript_task.cancel()


if __name__ == "__main__":
    asyncio.run(main())
